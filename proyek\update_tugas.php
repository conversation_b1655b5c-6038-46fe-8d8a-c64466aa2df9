<?php
session_start();
require '../koneksi.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $id = $_POST['id'];
    $status = $_POST['status'];
    $updated_by = $_SESSION['id_petugas'];

    // Update progress percentage berdasarkan status
    $progress_percentage = 0;
    switch($status) {
        case 'pending':
            $progress_percentage = 0;
            break;
        case 'proses':
            $progress_percentage = 50;
            break;
        case 'selesai':
            $progress_percentage = 100;
            break;
        case 'batal':
            $progress_percentage = 0;
            break;
        case 'verifikasi':
            $progress_percentage = 90;
            break;
    }

    // Update tugas proyek
    $update = mysqli_query($koneksi,
        "UPDATE tugas_proyek SET
         status='$status',
         progress_percentage='$progress_percentage',
         updated_at=NOW()
         WHERE id='$id'"
    );

    if ($update) {
        // Jika status diubah ke 'verifikasi', buat entry verifikasi baru
        if ($status == 'verifikasi') {
            // Cari file yang terkait dengan tugas ini
            $file_query = mysqli_query($koneksi, "SELECT id FROM file_gambar WHERE tugas_id='$id'");

            if (mysqli_num_rows($file_query) > 0) {
                // Ada file terkait, buat verifikasi untuk setiap file
                while ($file_row = mysqli_fetch_array($file_query)) {
                    $file_id = $file_row['id'];

                    // Cek apakah sudah ada entry verifikasi untuk file ini
                    $check_verification = mysqli_query($koneksi, "SELECT id FROM verifikasi WHERE file_id='$file_id' AND tugas_id='$id'");

                    if (mysqli_num_rows($check_verification) == 0) {
                        // Buat entry verifikasi baru
                        $insert_verification = mysqli_query($koneksi,
                            "INSERT INTO verifikasi (file_id, tugas_id, status_verifikasi, created_at)
                             VALUES ('$file_id', '$id', 'pending', NOW())"
                        );
                    } else {
                        // Update existing verification to pending
                        mysqli_query($koneksi,
                            "UPDATE verifikasi SET
                             status_verifikasi='pending',
                             verified_by=NULL,
                             verified_at=NULL
                             WHERE file_id='$file_id' AND tugas_id='$id'"
                        );
                    }
                }
            }
            // Note: If no files are associated with the task, no verification entries are created
        }

        // Set tanggal mulai jika status berubah ke proses
        if ($status == 'proses') {
            mysqli_query($koneksi,
                "UPDATE tugas_proyek SET tgl_mulai=CURDATE() WHERE id='$id' AND tgl_mulai IS NULL"
            );
        }

        // Set tanggal selesai jika status berubah ke selesai
        if ($status == 'selesai') {
            mysqli_query($koneksi,
                "UPDATE tugas_proyek SET tgl_selesai=CURDATE() WHERE id='$id'"
            );
        }

        $_SESSION['success_message'] = 'Status tugas berhasil diupdate!';
        header("Location: tugas_harian.php");
        exit;
    } else {
        $_SESSION['error_message'] = 'Gagal update status: ' . mysqli_error($koneksi);
        header("Location: tugas_harian.php");
        exit;
    }
}
?>
